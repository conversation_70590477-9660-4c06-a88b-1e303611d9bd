import React, { memo } from "react";
import { View, Text, Image, StyleSheet } from "react-native";
import { GLOBAL_STYLES } from "../../styles/globalStyles";
import { scale } from "../../utils/helpers/dimensionScale.helper";

// Import the QR code image from assets
const QR_CODE_IMAGE = require("../../assets/images/qr-image.png");

interface QRCodeSectionProps {
	title?: string;
	subtitle?: string;
	showTitle?: boolean;
	style?: any;
}

/**
 * QRCodeSection Component
 * Displays a QR code with customizable title and subtitle text
 * Used for subscription management and account access flows
 * Optimized with React.memo for performance
 */
const QRCodeSection: React.FC<QRCodeSectionProps> = memo(
	({
		title = "TO MANAGE ACCOUNT AND SUBSCRIPTION PLEASE FOLLOW THE QR CODE",
		subtitle,
		showTitle = true,
		style,
	}) => {
		return (
			<View style={[styles.container, style]}>
				{showTitle && (
					<View style={styles.textContainer}>
						<Text style={styles.title}>{title}</Text>
						{subtitle && (
							<Text style={styles.subtitle}>{subtitle}</Text>
						)}
					</View>
				)}
				<View style={styles.qrContainer}>
					<Image
						source={QR_CODE_IMAGE}
						style={styles.qrImage}
						resizeMode="contain"
						testID="qr-code-image"
					/>
				</View>
			</View>
		);
	}
);

const styles = StyleSheet.create({
	container: {
		alignItems: "center",
		justifyContent: "center",
		paddingVertical: scale(32),
	},
	textContainer: {
		marginBottom: scale(48),
		alignItems: "center",
		maxWidth: scale(600),
	},
	title: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(32),
		fontWeight: "bold",
		textAlign: "center",
		lineHeight: scale(42),
		letterSpacing: scale(1),
	},
	subtitle: {
		color: GLOBAL_STYLES.COLORS.TEXT_SECONDARY,
		fontSize: scale(24),
		textAlign: "center",
		marginTop: scale(16),
		lineHeight: scale(32),
	},
	qrContainer: {
		backgroundColor: "#ffffff",
		borderRadius: scale(16),
		padding: scale(24),
		shadowColor: "#000",
		shadowOffset: { width: 0, height: scale(4) },
		shadowOpacity: 0.3,
		shadowRadius: scale(8),
		elevation: 8,
	},
	qrImage: {
		width: scale(300),
		height: scale(300),
	},
});

// Set display name for debugging
QRCodeSection.displayName = "QRCodeSection";

export default QRCodeSection;
